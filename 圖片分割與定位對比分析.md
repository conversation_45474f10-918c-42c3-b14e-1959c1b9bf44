# 圖片分割與定位對比分析報告

## 測試概述

本次測試對比了 **Gemini 2.5 Flash** 和 **OpenRouter Deepseek R1** 兩個模型在 marker-pdf 中的圖片分割和定位效果。

## 測試結果摘要

| 指標 | Gemini 2.5 Flash | OpenRouter Deepseek R1 | 對比結果 |
|------|------------------|------------------------|----------|
| **處理時間** | 58.25 秒 | 55.10 秒 | Deepseek 快 5.4% |
| **文本長度** | 10,081 字符 | 10,081 字符 | **完全相同** |
| **圖片數量** | 1 張 | 1 張 | **完全相同** |
| **圖片文件名** | `_page_0_Picture_2.jpeg` | `_page_0_Picture_2.jpeg` | **完全相同** |
| **圖片在 Markdown 中的位置** | 第 11 行 | 第 11 行 | **完全相同** |

## 詳細對比分析

### 🖼️ 圖片分割效果

**結論：兩個模型的圖片分割效果完全一致**

1. **圖片數量**：兩個模型都識別並提取了 1 張圖片
2. **圖片命名**：都使用了相同的命名規則 `_page_0_Picture_2.jpeg`
3. **圖片內容**：提取的是同一張圖片（PDF 中的 logo 或圖標）

### 📍 圖片在 Markdown 中的定位

**結論：兩個模型的圖片定位完全一致**

兩個模型都將圖片放置在 Markdown 文檔的相同位置：

```markdown
A2232403C1PV1

![](_page_0_Picture_2.jpeg)

**A223** 
```

- **位置**：在文檔標識符 `A2232403C1PV1` 之後，課程代碼 `A223` 之前
- **語法**：都使用標準的 Markdown 圖片語法 `![](圖片路徑)`
- **上下文**：圖片位置符合原始 PDF 的布局邏輯

### 📝 文本識別對比

**結論：兩個模型的文本識別結果完全相同**

- **字符數**：都是 10,081 字符
- **內容結構**：標題、段落、列表格式完全一致
- **特殊字符**：都正確識別了粗體標記 `**`、項目符號等

### ⚡ 性能對比

| 性能指標 | Gemini 2.5 Flash | OpenRouter Deepseek R1 |
|----------|------------------|------------------------|
| **處理速度** | 58.25 秒 | 55.10 秒 ⭐ |
| **網絡需求** | 需要代理 | 無需代理 ⭐ |
| **成本** | 付費 API | 免費模型 ⭐ |
| **穩定性** | 有代理錯誤 | 無錯誤 ⭐ |

### 🔍 錯誤分析

**Gemini 2.5 Flash 出現的問題**：
```
2025-06-26 05:22:45,772 [ERROR] marker: APIError: 400 FAILED_PRECONDITION. 
{'error': {'code': 400, 'message': 'User location is not supported for the API use.', 'status': 'FAILED_PRECONDITION'}}
```

- 這是代理配置相關的錯誤
- 雖然最終處理成功，但影響了處理速度
- 說明 Gemini 對網絡環境更敏感

**OpenRouter Deepseek R1**：
- 無任何錯誤信息
- 處理過程順暢
- 網絡連接穩定

## 文件夾結構對比

兩個模型都生成了相同的文件夾結構：

```
模型名稱_時間戳/
├── ocr_result.md           # 完整的 OCR 文本內容
├── images_list.md          # 圖片清單文件
└── _page_0_Picture_2.jpeg  # 提取的圖片文件
```

## 結論與建議

### 🏆 總體結論

**兩個模型在圖片分割和定位方面表現完全一致**：
- ✅ 圖片識別數量相同
- ✅ 圖片命名規則相同  
- ✅ 圖片在 Markdown 中的位置相同
- ✅ 文本內容完全相同

### 💡 推薦建議

**推薦使用 OpenRouter Deepseek R1**，理由：

1. **🚀 性能優勢**：
   - 處理速度快 5.4%
   - 無網絡錯誤，穩定性更好

2. **💰 成本優勢**：
   - 使用免費模型
   - 無需付費 API

3. **🔧 配置優勢**：
   - 無需代理設置
   - 配置更簡單

4. **📊 質量保證**：
   - OCR 結果與 Gemini 完全相同
   - 圖片分割和定位效果一致

### 📁 文件位置

- **Gemini 結果**：`ocr_comparison_results/Gemini_2.5_Flash_20250626_052340/`
- **Deepseek 結果**：`ocr_comparison_results/OpenRouter_Deepseek_R1_20250626_052438/`
- **對比摘要**：`ocr_comparison_results/ocr_comparison_summary_20250626_052438.md`

您可以直接查看這些文件夾中的內容來驗證圖片分割和定位的效果。
