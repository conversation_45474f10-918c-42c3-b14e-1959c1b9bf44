# OCR 模型對比分析報告

## 測試概述

本次測試對比了兩個 LLM 模型在 marker-pdf OCR 處理中的性能表現：
- **Gemini 2.5 Flash** (通過 NordVPN 代理)
- **OpenRouter Deepseek R1** (直接連接，無需代理)

## 性能對比結果

| 指標 | Gemini 2.5 Flash | OpenRouter Deepseek R1 | 差異 |
|------|------------------|------------------------|------|
| **處理時間** | 84.29 秒 | 81.93 秒 | Deepseek 快 2.36 秒 (2.8%) |
| **文本長度** | 10,159 字符 | 9,991 字符 | Gemini 多 168 字符 (1.7%) |
| **圖片數量** | 2 | 2 | 相同 |
| **網絡需求** | 需要代理 | 無需代理 | Deepseek 更便利 |

## 詳細分析

### 🚀 速度性能
- **OpenRouter Deepseek R1** 略勝一籌，處理速度快約 2.8%
- 兩者差異很小，都在 80-85 秒範圍內
- 速度差異可能受網絡延遲影響（Gemini 需要通過代理）

### 📝 文本識別準確性
- **Gemini 2.5 Flash** 識別出的文本稍多一些（多 168 字符）
- 兩個模型都成功識別了相同的圖片數量
- 文本內容質量需要人工對比驗證

### 🌐 網絡配置便利性
- **OpenRouter Deepseek R1** 優勢明顯：
  - 無需配置代理
  - 直接 API 訪問
  - 配置更簡單
- **Gemini 2.5 Flash** 需要：
  - NordVPN 代理配置
  - 可能受代理穩定性影響

### 💰 成本考量
- **OpenRouter Deepseek R1**: 使用免費模型 `deepseek/deepseek-r1-0528:free`
- **Gemini 2.5 Flash**: 需要 Google API 付費使用

## 建議

### 推薦使用 OpenRouter Deepseek R1，原因：
1. ✅ **配置簡單** - 無需代理設置
2. ✅ **成本更低** - 免費模型可用
3. ✅ **速度略快** - 處理時間稍短
4. ✅ **穩定性好** - 不依賴代理連接

### Gemini 2.5 Flash 的優勢：
1. ✅ **文本識別稍多** - 可能在某些場景下更準確
2. ✅ **Google 生態** - 如果已有 Google Cloud 環境

## 技術實現

測試腳本已更新為使用環境變量配置，支持：
- 自動切換代理設置
- 批量測試多個模型
- 自動生成對比報告
- Markdown 格式輸出便於查看

## 結論

對於大多數 OCR 使用場景，**推薦使用 OpenRouter Deepseek R1**，因為它提供了更好的便利性和成本效益，同時保持了相當的處理性能。

如果對文本識別的精確度有極高要求，可以考慮使用 Gemini 2.5 Flash，但需要權衡額外的配置複雜度和成本。
