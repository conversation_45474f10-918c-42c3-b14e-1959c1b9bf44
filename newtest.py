import os
import sys
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

# 載入環境變量
load_dotenv()

def create_gemini_config():
    """創建 Gemini 模型配置"""
    # 設置代理（Gemini 需要代理）
    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
    if nordvpn_proxy:
        os.environ['HTTPS_PROXY'] = nordvpn_proxy
        os.environ['HTTP_PROXY'] = nordvpn_proxy

    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_GEMINI_SERIVES'),
        "gemini_api_key": os.getenv('GEMINI_API_KEY'),
        "gemini_model_name": os.getenv('GEMINI_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true'
    }
    return config

def create_openrouter_config():
    """創建 OpenRouter Deepseek 模型配置"""
    # 清除代理設置（OpenRouter 不需要代理）
    if 'HTTPS_PROXY' in os.environ:
        del os.environ['HTTPS_PROXY']
    if 'HTTP_PROXY' in os.environ:
        del os.environ['HTTP_PROXY']

    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_OPENROUTER_SERIVES'),
        "openai_api_key": os.getenv('OPENROUTER_API_KEY'),
        "openai_base_url": os.getenv('OPENROUTER_URL'),
        "openai_model": os.getenv('OPENROUTER_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true'
    }
    return config

def save_model_output(text, images, model_name, timestamp, base_dir="ocr_comparison_results"):
    """保存模型輸出到獨立文件夾，包括 markdown 文件和圖片"""
    # 創建模型專用文件夾
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)

    # 保存 markdown 文件
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR 結果\n\n")
        f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 提取的文本內容\n\n")
        f.write(text)

    # 保存圖片文件
    saved_images = []
    for i, (image_name, image_data) in enumerate(images.items()):
        # 使用原始圖片名稱，如果沒有則使用索引
        if image_name:
            image_filename = f"{image_name}"
        else:
            image_filename = f"image_{i+1}.png"

        image_path = model_folder / image_filename

        # 保存圖片
        try:
            image_data.save(image_path)
            saved_images.append(image_filename)
            print(f"  保存圖片: {image_filename}")
        except Exception as e:
            print(f"  保存圖片失敗 {image_filename}: {e}")

    # 創建圖片清單文件
    if saved_images:
        images_list_file = model_folder / "images_list.md"
        with open(images_list_file, 'w', encoding='utf-8') as f:
            f.write(f"# {model_name} 提取的圖片清單\n\n")
            f.write(f"總共提取了 {len(saved_images)} 張圖片：\n\n")
            for img_name in saved_images:
                f.write(f"- ![{img_name}]({img_name})\n")

    print(f"  模型輸出已保存到: {model_folder}")
    return model_folder

def test_model(config, model_name, pdf_path="sample.pdf"):
    """測試指定模型的 OCR 性能"""
    print(f"\n{'='*50}")
    print(f"開始測試 {model_name}")
    print(f"{'='*50}")

    try:
        # 創建配置解析器和轉換器
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
            llm_service=config_parser.get_llm_service()
        )

        # 記錄開始時間
        start_time = time.time()
        print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 執行 OCR 轉換
        rendered = converter(pdf_path)
        text, _, images = text_from_rendered(rendered)

        # 記錄結束時間
        end_time = time.time()
        processing_time = end_time - start_time

        print(f"結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"處理時間: {processing_time:.2f} 秒")
        print(f"文本長度: {len(text)} 字符")
        print(f"圖片數量: {len(images)}")

        # 保存模型輸出到獨立文件夾
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_folder = save_model_output(text, images, model_name, timestamp)

        return {
            'model_name': model_name,
            'processing_time': processing_time,
            'text_length': len(text),
            'image_count': len(images),
            'text_content': text,
            'images': images,
            'output_folder': output_folder,
            'success': True,
            'error': None
        }

    except Exception as e:
        print(f"錯誤: {str(e)}")
        return {
            'model_name': model_name,
            'processing_time': 0,
            'text_length': 0,
            'image_count': 0,
            'text_content': '',
            'images': {},
            'output_folder': None,
            'success': False,
            'error': str(e)
        }

def save_results_to_markdown(results, output_dir="ocr_comparison_results"):
    """將測試結果保存為 Markdown 文件"""
    # 創建輸出目錄
    Path(output_dir).mkdir(exist_ok=True)

    # 生成時間戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 創建對比報告
    comparison_file = Path(output_dir) / f"ocr_comparison_summary_{timestamp}.md"

    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("# OCR 模型對比測試報告\n\n")
        f.write(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 性能對比表格
        f.write("## 性能對比\n\n")
        f.write("| 模型 | 處理時間(秒) | 文本長度(字符) | 圖片數量 | 輸出文件夾 | 狀態 |\n")
        f.write("|------|-------------|---------------|----------|------------|------|\n")

        for result in results:
            status = "✅ 成功" if result['success'] else "❌ 失敗"
            folder_name = result['output_folder'].name if result['output_folder'] else "N/A"
            f.write(f"| {result['model_name']} | {result['processing_time']:.2f} | {result['text_length']} | {result['image_count']} | `{folder_name}` | {status} |\n")

        f.write("\n")

        # 文件夾結構說明
        f.write("## 輸出文件夾結構\n\n")
        f.write("每個模型的結果都保存在獨立的文件夾中，包含以下文件：\n\n")
        f.write("```\n")
        f.write("模型名稱_時間戳/\n")
        f.write("├── ocr_result.md      # OCR 提取的完整文本內容\n")
        f.write("├── images_list.md     # 提取的圖片清單\n")
        f.write("├── image_1.png        # 提取的圖片文件\n")
        f.write("├── image_2.png        # ...\n")
        f.write("└── ...\n")
        f.write("```\n\n")

        # 詳細結果摘要
        for result in results:
            f.write(f"## {result['model_name']} 結果摘要\n\n")

            if result['success']:
                f.write(f"- **處理時間**: {result['processing_time']:.2f} 秒\n")
                f.write(f"- **文本長度**: {result['text_length']} 字符\n")
                f.write(f"- **圖片數量**: {result['image_count']}\n")
                f.write(f"- **輸出文件夾**: `{result['output_folder'].name}`\n\n")
                f.write("### 圖片分割情況\n\n")
                if result['images']:
                    for img_name in result['images'].keys():
                        f.write(f"- {img_name}\n")
                else:
                    f.write("- 無圖片提取\n")
                f.write(f"\n**完整內容請查看**: `{result['output_folder']}/ocr_result.md`\n\n")
            else:
                f.write(f"- **錯誤**: {result['error']}\n\n")

    print(f"\n對比摘要報告已保存至: {comparison_file}")
    return comparison_file

def main():
    """主函數：執行 OCR 模型對比測試"""
    print("開始 OCR 模型對比測試")
    print("測試模型: Gemini 2.5 Flash vs OpenRouter Deepseek")

    results = []

    # 測試 Gemini 模型
    gemini_config = create_gemini_config()
    gemini_result = test_model(gemini_config, "Gemini 2.5 Flash")
    results.append(gemini_result)

    # 測試 OpenRouter Deepseek 模型
    openrouter_config = create_openrouter_config()
    deepseek_result = test_model(openrouter_config, "OpenRouter Deepseek R1")
    results.append(deepseek_result)

    # 保存結果到 Markdown 文件
    report_file = save_results_to_markdown(results)

    print(f"\n{'='*50}")
    print("測試完成！")
    print(f"詳細對比報告請查看: {report_file}")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()