#!/usr/bin/env python3
"""
測試方法2：使用 google-genai SDK 直接配置 proxy 調用 Gemini 服務
"""
import os
import sys
import time
from google import genai
from google.genai import types
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

def test_method2_sdk_proxy():
    """測試方法2：google-genai SDK 直接配置代理"""
    print("=" * 60)
    print("測試方法2：google-genai SDK 直接配置 NordVPN SOCKS5 代理")
    print("=" * 60)
    
    # 清除環境變量中的代理設置，確保只使用 SDK 配置
    print("清除環境變量中的代理設置...")
    for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
        if proxy_var in os.environ:
            del os.environ[proxy_var]
            print(f"已清除 {proxy_var}")
    
    # NordVPN SOCKS5 代理配置
    nordvpn_proxy = "socks5://2qfCDkqLEw8P3kf4ZAdm5zMu:<EMAIL>:1080"
    
    # 方法2：直接配置 google-genai SDK
    print("配置 google-genai SDK 代理...")
    try:
        http_options = types.HttpOptions(
            client_args={'proxy': nordvpn_proxy},
            async_client_args={'proxy': nordvpn_proxy},
        )
        
        client = genai.Client(
            api_key="AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs",
            http_options=http_options
        )
        print("✓ google-genai 客戶端初始化成功")
        
        # 測試 Gemini API 連接
        print("測試 Gemini API 連接...")
        try:
            # 簡單的 API 測試
            response = client.models.generate_content(
                model='gemini-2.5-flash',
                contents='Hello, this is a test message. Please respond briefly.'
            )
            print("✓ Gemini API 連接測試成功")
            print(f"API 響應：{response.text[:100]}...")
        except Exception as api_error:
            print(f"⚠️  Gemini API 連接測試失敗：{str(api_error)}")
            # 繼續進行 marker-pdf 測試
        
    except Exception as e:
        print(f"❌ google-genai 客戶端初始化失敗：{str(e)}")
        return False
    
    # 設置 API key 環境變量（marker-pdf 需要）
    os.environ['GOOGLE_API_KEY'] = "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs"
    
    # 配置 marker-pdf
    config = {
        "use_llm": True,
        "llm_service": "marker.services.gemini.GoogleGeminiService",
        "gemini_api_key": "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs",
        "gemini_model_name": "gemini-2.5-flash",
        "format_lines": True,
        "output_format": "markdown"
    }
    
    print("\n初始化 marker-pdf 轉換器...")
    try:
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
        )
        print("✓ 轉換器初始化成功")
        
        # 檢查是否有 sample.pdf 文件
        if not os.path.exists("sample.pdf"):
            print("⚠️  警告：sample.pdf 文件不存在，無法進行完整測試")
            print("請確保工作目錄中有 sample.pdf 文件")
            return False
        
        # 转换 PDF
        print("\n開始轉換 PDF...")
        start_time = time.time()
        
        rendered = converter("sample.pdf")
        text, _, images = text_from_rendered(rendered)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✓ PDF 轉換完成！耗時：{processing_time:.2f} 秒")
        print(f"✓ 提取的文本長度：{len(text)} 字符")
        print(f"✓ 提取的圖片數量：{len(images) if images else 0}")
        
        # 顯示部分文本內容（前500字符）
        if text:
            print("\n--- 提取的文本內容（前500字符）---")
            print(text[:500])
            if len(text) > 500:
                print("...")
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤：{str(e)}")
        print(f"錯誤類型：{type(e).__name__}")
        import traceback
        print("詳細錯誤信息：")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始測試方法2：google-genai SDK 直接配置代理")
    success = test_method2_sdk_proxy()
    
    if success:
        print("\n🎉 方法2測試成功！google-genai SDK 直接配置代理有效")
    else:
        print("\n❌ 方法2測試失敗！google-genai SDK 直接配置代理可能無效")
    
    print("\n測試完成")
