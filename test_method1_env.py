#!/usr/bin/env python3
"""
測試方法1：使用環境變量配置 proxy 調用 Gemini 服務
"""
import os
import sys
import time
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

def test_method1_env_proxy():
    """測試方法1：環境變量配置代理"""
    print("=" * 60)
    print("測試方法1：環境變量配置 NordVPN SOCKS5 代理")
    print("=" * 60)
    
    # NordVPN SOCKS5 代理配置
    nordvpn_proxy = "socks5://2qfCDkqLEw8P3kf4ZAdm5zMu:<EMAIL>:1080"
    
    # 方法1：环境变量配置
    print("設置環境變量...")
    os.environ['HTTPS_PROXY'] = nordvpn_proxy
    os.environ['HTTP_PROXY'] = nordvpn_proxy
    os.environ['GOOGLE_API_KEY'] = "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs"
    
    print(f"HTTP_PROXY: {os.environ.get('HTTP_PROXY', 'Not set')}")
    print(f"HTTPS_PROXY: {os.environ.get('HTTPS_PROXY', 'Not set')}")
    print(f"GOOGLE_API_KEY: {'已設置' if os.environ.get('GOOGLE_API_KEY') else '未設置'}")
    
    # 配置 marker-pdf
    config = {
        "use_llm": True,
        "llm_service": "marker.services.gemini.GoogleGeminiService",
        "gemini_api_key": "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs",
        "gemini_model_name": "gemini-2.5-flash",
        "format_lines": True,
        "output_format": "markdown"
    }
    
    print("\n初始化 marker-pdf 轉換器...")
    try:
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
        )
        print("✓ 轉換器初始化成功")
        
        # 檢查是否有 sample.pdf 文件
        if not os.path.exists("sample.pdf"):
            print("⚠️  警告：sample.pdf 文件不存在，無法進行完整測試")
            print("請確保工作目錄中有 sample.pdf 文件")
            return False
        
        # 转换 PDF
        print("\n開始轉換 PDF...")
        start_time = time.time()
        
        rendered = converter("sample.pdf")
        text, _, images = text_from_rendered(rendered)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✓ PDF 轉換完成！耗時：{processing_time:.2f} 秒")
        print(f"✓ 提取的文本長度：{len(text)} 字符")
        print(f"✓ 提取的圖片數量：{len(images) if images else 0}")
        
        # 顯示部分文本內容（前500字符）
        if text:
            print("\n--- 提取的文本內容（前500字符）---")
            print(text[:500])
            if len(text) > 500:
                print("...")
        
        return True
        
    except Exception as e:
        print(f"❌ 錯誤：{str(e)}")
        print(f"錯誤類型：{type(e).__name__}")
        import traceback
        print("詳細錯誤信息：")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始測試方法1：環境變量配置代理")
    success = test_method1_env_proxy()
    
    if success:
        print("\n🎉 方法1測試成功！環境變量配置代理有效")
    else:
        print("\n❌ 方法1測試失敗！環境變量配置代理可能無效")
    
    print("\n測試完成")
