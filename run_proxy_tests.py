#!/usr/bin/env python3
"""
運行兩種 proxy 配置方法的測試
"""
import os
import sys
import subprocess
import time

def run_test(test_file, test_name):
    """運行單個測試文件"""
    print(f"\n{'='*80}")
    print(f"開始運行 {test_name}")
    print(f"測試文件：{test_file}")
    print(f"{'='*80}")
    
    try:
        # 運行測試
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=300)  # 5分鐘超時
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {test_name} 執行成功")
            return True
        else:
            print(f"❌ {test_name} 執行失敗，返回碼：{result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {test_name} 執行超時（5分鐘）")
        return False
    except Exception as e:
        print(f"❌ 運行 {test_name} 時發生錯誤：{str(e)}")
        return False

def main():
    """主函數"""
    print("🚀 開始測試兩種 proxy 配置方法")
    print(f"當前工作目錄：{os.getcwd()}")
    
    # 檢查必要文件
    required_files = ["test_method1_env.py", "test_method2_sdk.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要的測試文件：{missing_files}")
        return
    
    # 檢查 sample.pdf
    if not os.path.exists("sample.pdf"):
        print("⚠️  警告：sample.pdf 文件不存在")
        print("測試將會運行，但無法進行完整的 PDF 轉換測試")
        print("請確保工作目錄中有 sample.pdf 文件以進行完整測試")
    
    results = {}
    
    # 測試方法1：環境變量配置
    print("\n" + "🔄" * 20)
    print("準備測試方法1：環境變量配置代理")
    time.sleep(2)
    results["方法1"] = run_test("test_method1_env.py", "方法1：環境變量配置")
    
    # 等待一段時間，確保環境清理
    print("\n⏳ 等待 5 秒，準備下一個測試...")
    time.sleep(5)
    
    # 測試方法2：SDK 直接配置
    print("\n" + "🔄" * 20)
    print("準備測試方法2：google-genai SDK 直接配置代理")
    time.sleep(2)
    results["方法2"] = run_test("test_method2_sdk.py", "方法2：SDK 直接配置")
    
    # 總結結果
    print("\n" + "="*80)
    print("📊 測試結果總結")
    print("="*80)
    
    for method, success in results.items():
        status = "✅ 成功" if success else "❌ 失敗"
        print(f"{method}：{status}")
    
    successful_methods = [method for method, success in results.items() if success]
    
    if len(successful_methods) == 2:
        print("\n🎉 兩種方法都測試成功！")
        print("建議：")
        print("- 方法1（環境變量）更簡單，適合全局代理設置")
        print("- 方法2（SDK配置）更精確，適合特定應用的代理設置")
    elif len(successful_methods) == 1:
        print(f"\n⚠️  只有 {successful_methods[0]} 測試成功")
        print("建議使用成功的方法進行後續開發")
    else:
        print("\n❌ 兩種方法都測試失敗")
        print("請檢查：")
        print("1. 網絡連接是否正常")
        print("2. NordVPN 代理配置是否正確")
        print("3. Google API Key 是否有效")
        print("4. 相關依賴包是否正確安裝")
    
    print("\n測試完成！")

if __name__ == "__main__":
    main()
